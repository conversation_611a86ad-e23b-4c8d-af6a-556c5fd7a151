<?php
/**
 * Custom Page Template for Alabama State Page
 * Template Name: Alabama State Page
 */

get_header();

// Add Google Maps API script
wp_enqueue_script('google-maps', 'https://maps.googleapis.com/maps/api/js?key=AIzaSyDSY9K8KUaLvpm42ubwTCU3jJKtzZ8GXmA&callback=initAlabamaZoosMap', array(), null, true);

// Get Alabama location term for data
$alabama_term = get_term_by('slug', 'alabama', 'location');
$zoo_count = $alabama_term ? $alabama_term->count : 5; // fallback to 5 if term not found
?>

<div id="primary" class="content-area full-width-content">
    <main id="main" class="site-main">

        <!-- Enhanced Page Header - Zoo Style -->
        <div class="city-hero-section">
            <div class="hero-overlay"></div>
            <div class="hero-content-wrapper">
                <div class="hero-content">
                    <div class="breadcrumbs text-center">
                        <a href="<?php echo home_url(); ?>">Home</a>
                        <span class="separator">›</span>
                        <span class="current">Alabama</span>
                    </div>

                    <h1 class="city-title">
                        Best Petting Zoos in Alabama
                    </h1>

                    <p class="hero-subtitle text-center">
                        Explore <?php echo $zoo_count; ?> petting zoos across Alabama
                    </p>
                </div>
            </div>
        </div>

        <!-- Main Content Container -->
        <div class="container" style="max-width: 1400px; margin: 0 auto; padding: 0 2rem; text-align: center;">

            <!-- Original Page Content -->
            <?php while (have_posts()) : the_post(); ?>
                <div class="original-content" style="text-align: left; margin-bottom: 4rem;">
                    <?php
                    // Get the content and remove the title since we're using it in the hero
                    $content = get_the_content();
                    $content = apply_filters('the_content', $content);
                    echo $content;
                    ?>
                </div>
            <?php endwhile; ?>

        </div>

        <!-- New SEO-Focused H2 Sections -->
        <div class="container" style="max-width: 1400px; margin: 0 auto; padding: 0 2rem;">

            <!-- Best Petting Zoos in Alabama by Ranking -->
            <section class="state-ranking-section">
                <h2>Best Petting Zoos in Alabama by Ranking</h2>
                <p>Discover the highest-rated petting zoos across Alabama based on visitor reviews and family experiences. These top-ranked destinations consistently deliver exceptional animal encounters and memorable family adventures throughout the state.</p>
                
                <div class="petting-zoo-cards-grid" id="state-ranking-zoos">
                    <?php
                    // Get top-rated petting zoos in Alabama
                    $ranking_args = array(
                        'post_type' => 'petting_zoo',
                        'posts_per_page' => 3,
                        'meta_key' => 'rating',
                        'orderby' => 'meta_value_num',
                        'order' => 'DESC',
                        'tax_query' => array(
                            array(
                                'taxonomy' => 'location',
                                'field' => 'slug',
                                'terms' => 'alabama',
                            )
                        )
                    );
                    
                    $ranking_query = new WP_Query($ranking_args);
                    
                    if ($ranking_query->have_posts()) :
                        while ($ranking_query->have_posts()) : $ranking_query->the_post();
                            // Get zoo data
                            $zoo_data = get_post_meta(get_the_ID(), 'zoo_data', true);
                            $zoo_data = $zoo_data ? json_decode($zoo_data, true) : array();
                            
                            $rating = isset($zoo_data['rating']) ? $zoo_data['rating'] : 4.5;
                            $address = isset($zoo_data['postalAddress']) ? $zoo_data['postalAddress'] : array();
                            $full_address = '';
                            if (!empty($address)) {
                                $address_parts = array();
                                if (!empty($address['addressLines'])) {
                                    $address_parts[] = $address['addressLines'][0];
                                }
                                if (!empty($address['locality'])) {
                                    $address_parts[] = $address['locality'];
                                }
                                if (!empty($address['administrativeArea'])) {
                                    $address_parts[] = $address['administrativeArea'];
                                }
                                if (!empty($address['postalCode'])) {
                                    $address_parts[] = $address['postalCode'];
                                }
                                $full_address = implode(', ', $address_parts);
                            }
                            
                            // Get features/activities
                            $features = get_the_terms(get_the_ID(), 'features');
                            $feature_names = array();
                            if ($features && !is_wp_error($features)) {
                                foreach ($features as $feature) {
                                    $feature_names[] = $feature->name;
                                }
                            }
                            ?>
                            <div class="petting-zoo-card">
                                <div class="card-image">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', array('alt' => get_the_title(), 'loading' => 'lazy')); ?>
                                        <?php else : ?>
                                            <img src="<?php echo wp_upload_dir()['baseurl']; ?>/2025/06/pettingzoophoto%20(1).webp" alt="<?php the_title(); ?>" loading="lazy">
                                        <?php endif; ?>
                                    </a>
                                    <?php if ($rating) : ?>
                                        <div class="rating-badge">
                                            <span class="rating-number"><?php echo number_format($rating, 1); ?></span>
                                            <span class="rating-star">★</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="card-content">
                                    <h3 class="zoo-name text-center">
                                        <?php the_title(); ?>
                                    </h3>

                                    <?php if ($full_address) : ?>
                                        <div class="zoo-location text-center">
                                            <span class="location-icon">📍</span>
                                            <?php echo esc_html($full_address); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($feature_names)) : ?>
                                        <div class="activities-section text-center">
                                            <div class="activity-tags">
                                                <?php
                                                $displayed_features = array_slice($feature_names, 0, 4);
                                                foreach ($displayed_features as $feature) : ?>
                                                    <span class="activity-tag"><?php echo esc_html($feature); ?></span>
                                                <?php endforeach; ?>
                                                <?php if (count($feature_names) > 4) : ?>
                                                    <span class="activity-tag more-tag">+<?php echo count($feature_names) - 4; ?> more</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="card-description text-center">
                                        <?php echo wp_trim_words(get_the_excerpt(), 10, '...'); ?>
                                    </div>

                                    <a href="<?php the_permalink(); ?>" class="view-details-btn">
                                        View Details →
                                    </a>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
            </section>

            <!-- Best Petting Zoos for Birthday Parties in Alabama -->
            <section class="state-birthday-section">
                <h2>Best Petting Zoos for Birthday Parties in Alabama</h2>
                <p>Make your child's birthday unforgettable with these Alabama petting zoos that specialize in birthday celebrations. From party packages to private animal encounters, these venues across the state create magical memories for the special day.</p>
                
                <div class="petting-zoo-cards-grid" id="state-birthday-zoos">
                    <?php
                    // Get petting zoos suitable for birthday parties
                    $birthday_args = array(
                        'post_type' => 'petting_zoo',
                        'posts_per_page' => 3,
                        'tax_query' => array(
                            'relation' => 'AND',
                            array(
                                'taxonomy' => 'location',
                                'field' => 'slug',
                                'terms' => 'alabama',
                            ),
                            array(
                                'taxonomy' => 'features',
                                'field' => 'slug',
                                'terms' => array('birthday-parties', 'party-packages', 'private-events'),
                                'operator' => 'IN'
                            )
                        )
                    );
                    
                    $birthday_query = new WP_Query($birthday_args);
                    
                    if (!$birthday_query->have_posts()) {
                        // Fallback: get any Alabama zoos if no birthday-specific ones found
                        $birthday_args['tax_query'] = array(
                            array(
                                'taxonomy' => 'location',
                                'field' => 'slug',
                                'terms' => 'alabama',
                            )
                        );
                        $birthday_query = new WP_Query($birthday_args);
                    }
                    
                    if ($birthday_query->have_posts()) :
                        while ($birthday_query->have_posts()) : $birthday_query->the_post();
                            // Get zoo data
                            $zoo_data = get_post_meta(get_the_ID(), 'zoo_data', true);
                            $zoo_data = $zoo_data ? json_decode($zoo_data, true) : array();

                            $rating = isset($zoo_data['rating']) ? $zoo_data['rating'] : 4.5;
                            $address = isset($zoo_data['postalAddress']) ? $zoo_data['postalAddress'] : array();
                            $full_address = '';
                            if (!empty($address)) {
                                $address_parts = array();
                                if (!empty($address['addressLines'])) {
                                    $address_parts[] = $address['addressLines'][0];
                                }
                                if (!empty($address['locality'])) {
                                    $address_parts[] = $address['locality'];
                                }
                                if (!empty($address['administrativeArea'])) {
                                    $address_parts[] = $address['administrativeArea'];
                                }
                                if (!empty($address['postalCode'])) {
                                    $address_parts[] = $address['postalCode'];
                                }
                                $full_address = implode(', ', $address_parts);
                            }

                            // Get features/activities
                            $features = get_the_terms(get_the_ID(), 'features');
                            $feature_names = array();
                            if ($features && !is_wp_error($features)) {
                                foreach ($features as $feature) {
                                    $feature_names[] = $feature->name;
                                }
                            }
                            // Get zoo data for consistent formatting
                            $zoo_data = get_post_meta(get_the_ID(), 'zoo_data', true);
                            $zoo_data = $zoo_data ? json_decode($zoo_data, true) : array();

                            $rating = isset($zoo_data['rating']) ? $zoo_data['rating'] : 4.5;
                            $address = isset($zoo_data['postalAddress']) ? $zoo_data['postalAddress'] : array();
                            $full_address = '';
                            if (!empty($address)) {
                                $address_parts = array();
                                if (!empty($address['addressLines'])) {
                                    $address_parts[] = $address['addressLines'][0];
                                }
                                if (!empty($address['locality'])) {
                                    $address_parts[] = $address['locality'];
                                }
                                if (!empty($address['administrativeArea'])) {
                                    $address_parts[] = $address['administrativeArea'];
                                }
                                if (!empty($address['postalCode'])) {
                                    $address_parts[] = $address['postalCode'];
                                }
                                $full_address = implode(', ', $address_parts);
                            }

                            // Get features/activities
                            $features = get_the_terms(get_the_ID(), 'features');
                            $feature_names = array();
                            if ($features && !is_wp_error($features)) {
                                foreach ($features as $feature) {
                                    $feature_names[] = $feature->name;
                                }
                            }
                            ?>
                            <div class="petting-zoo-card">
                                <div class="card-image">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', array('alt' => get_the_title(), 'loading' => 'lazy')); ?>
                                        <?php else : ?>
                                            <img src="<?php echo wp_upload_dir()['baseurl']; ?>/2025/06/pettingzoophoto%20(1).webp" alt="<?php the_title(); ?>" loading="lazy">
                                        <?php endif; ?>
                                    </a>
                                    <?php if ($rating) : ?>
                                        <div class="rating-badge">
                                            <span class="rating-number"><?php echo number_format($rating, 1); ?></span>
                                            <span class="rating-star">★</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="card-content">
                                    <h3 class="zoo-name text-center">
                                        <?php the_title(); ?>
                                    </h3>

                                    <?php if ($full_address) : ?>
                                        <div class="zoo-location text-center">
                                            <span class="location-icon">📍</span>
                                            <?php echo esc_html($full_address); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($feature_names)) : ?>
                                        <div class="activities-section text-center">
                                            <div class="activity-tags">
                                                <?php
                                                $displayed_features = array_slice($feature_names, 0, 4);
                                                foreach ($displayed_features as $feature) : ?>
                                                    <span class="activity-tag"><?php echo esc_html($feature); ?></span>
                                                <?php endforeach; ?>
                                                <?php if (count($feature_names) > 4) : ?>
                                                    <span class="activity-tag more-tag">+<?php echo count($feature_names) - 4; ?> more</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="card-description text-center">
                                        <?php echo wp_trim_words(get_the_excerpt(), 10, '...'); ?>
                                    </div>

                                    <a href="<?php the_permalink(); ?>" class="view-details-btn">
                                        View Details →
                                    </a>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
            </section>

            <!-- Best Mobile Petting Zoos in Alabama -->
            <section class="state-mobile-section">
                <h2>Best Mobile Petting Zoos in Alabama</h2>
                <p>Bring the petting zoo experience directly to your location with these mobile petting zoo services across Alabama. Perfect for birthday parties, school events, and community gatherings, these mobile services deliver hands-on animal experiences right to your doorstep.</p>

                <div class="petting-zoo-cards-grid" id="state-mobile-zoos">
                    <?php
                    // Get mobile petting zoos in Alabama
                    $mobile_args = array(
                        'post_type' => 'petting_zoo',
                        'posts_per_page' => 3,
                        'tax_query' => array(
                            'relation' => 'AND',
                            array(
                                'taxonomy' => 'location',
                                'field' => 'slug',
                                'terms' => 'alabama',
                            ),
                            array(
                                'taxonomy' => 'features',
                                'field' => 'slug',
                                'terms' => array('mobile-petting-zoo', 'mobile-service', 'traveling-zoo'),
                                'operator' => 'IN'
                            )
                        )
                    );

                    $mobile_query = new WP_Query($mobile_args);

                    if (!$mobile_query->have_posts()) {
                        // Fallback: get any Alabama zoos if no mobile-specific ones found
                        $mobile_args['tax_query'] = array(
                            array(
                                'taxonomy' => 'location',
                                'field' => 'slug',
                                'terms' => 'alabama',
                            )
                        );
                        $mobile_query = new WP_Query($mobile_args);
                    }

                    if ($mobile_query->have_posts()) :
                        $card_count = 0;
                        while ($mobile_query->have_posts() && $card_count < 3) : $mobile_query->the_post();
                            $card_count++;
                            // Get zoo data for consistent formatting
                            $zoo_data = get_post_meta(get_the_ID(), 'zoo_data', true);
                            $zoo_data = $zoo_data ? json_decode($zoo_data, true) : array();

                            $rating = isset($zoo_data['rating']) ? $zoo_data['rating'] : 4.5;
                            $address = isset($zoo_data['postalAddress']) ? $zoo_data['postalAddress'] : array();
                            $full_address = '';
                            if (!empty($address)) {
                                $address_parts = array();
                                if (!empty($address['addressLines'])) {
                                    $address_parts[] = $address['addressLines'][0];
                                }
                                if (!empty($address['locality'])) {
                                    $address_parts[] = $address['locality'];
                                }
                                if (!empty($address['administrativeArea'])) {
                                    $address_parts[] = $address['administrativeArea'];
                                }
                                if (!empty($address['postalCode'])) {
                                    $address_parts[] = $address['postalCode'];
                                }
                                $full_address = implode(', ', $address_parts);
                            }

                            // Get features/activities
                            $features = get_the_terms(get_the_ID(), 'features');
                            $feature_names = array();
                            if ($features && !is_wp_error($features)) {
                                foreach ($features as $feature) {
                                    $feature_names[] = $feature->name;
                                }
                            }
                            ?>
                            <div class="petting-zoo-card">
                                <div class="card-image">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', array('alt' => get_the_title(), 'loading' => 'lazy')); ?>
                                        <?php else : ?>
                                            <img src="<?php echo wp_upload_dir()['baseurl']; ?>/2025/06/pettingzoophoto%20(1).webp" alt="<?php the_title(); ?>" loading="lazy">
                                        <?php endif; ?>
                                    </a>
                                    <?php if ($rating) : ?>
                                        <div class="rating-badge">
                                            <span class="rating-number"><?php echo number_format($rating, 1); ?></span>
                                            <span class="rating-star">★</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="card-content">
                                    <h3 class="zoo-name text-center">
                                        <?php the_title(); ?>
                                    </h3>

                                    <?php if ($full_address) : ?>
                                        <div class="zoo-location text-center">
                                            <span class="location-icon">📍</span>
                                            <?php echo esc_html($full_address); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($feature_names)) : ?>
                                        <div class="activities-section text-center">
                                            <div class="activity-tags">
                                                <?php
                                                $displayed_features = array_slice($feature_names, 0, 4);
                                                foreach ($displayed_features as $feature) : ?>
                                                    <span class="activity-tag"><?php echo esc_html($feature); ?></span>
                                                <?php endforeach; ?>
                                                <?php if (count($feature_names) > 4) : ?>
                                                    <span class="activity-tag more-tag">+<?php echo count($feature_names) - 4; ?> more</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="card-description text-center">
                                        <?php echo wp_trim_words(get_the_excerpt(), 10, '...'); ?>
                                    </div>

                                    <a href="<?php the_permalink(); ?>" class="view-details-btn">
                                        View Details →
                                    </a>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
            </section>

            <!-- Best Petting Zoos with Bunnies in Alabama -->
            <section class="state-bunnies-section">
                <h2>Best Petting Zoos in Alabama with Bunnies</h2>
                <p>Discover Alabama petting zoos that feature adorable bunnies and rabbits for gentle interactions. These family-friendly destinations offer special bunny encounters that are perfect for young children and create heartwarming memories with these soft, cuddly animals.</p>

                <div class="petting-zoo-cards-grid" id="state-bunnies-zoos">
                    <?php
                    // Get petting zoos with bunnies/rabbits
                    $bunnies_args = array(
                        'post_type' => 'petting_zoo',
                        'posts_per_page' => 3,
                        'tax_query' => array(
                            'relation' => 'AND',
                            array(
                                'taxonomy' => 'location',
                                'field' => 'slug',
                                'terms' => 'alabama',
                            ),
                            array(
                                'taxonomy' => 'animals',
                                'field' => 'slug',
                                'terms' => array('bunnies', 'rabbits', 'bunny'),
                                'operator' => 'IN'
                            )
                        )
                    );

                    $bunnies_query = new WP_Query($bunnies_args);

                    if (!$bunnies_query->have_posts()) {
                        // Fallback: get any Alabama zoos
                        $bunnies_args['tax_query'] = array(
                            array(
                                'taxonomy' => 'location',
                                'field' => 'slug',
                                'terms' => 'alabama',
                            )
                        );
                        $bunnies_query = new WP_Query($bunnies_args);
                    }

                    if ($bunnies_query->have_posts()) :
                        $card_count = 0;
                        while ($bunnies_query->have_posts() && $card_count < 3) : $bunnies_query->the_post();
                            $card_count++;
                            // Get zoo data for consistent formatting
                            $zoo_data = get_post_meta(get_the_ID(), 'zoo_data', true);
                            $zoo_data = $zoo_data ? json_decode($zoo_data, true) : array();

                            $rating = isset($zoo_data['rating']) ? $zoo_data['rating'] : 4.5;
                            $address = isset($zoo_data['postalAddress']) ? $zoo_data['postalAddress'] : array();
                            $full_address = '';
                            if (!empty($address)) {
                                $address_parts = array();
                                if (!empty($address['addressLines'])) {
                                    $address_parts[] = $address['addressLines'][0];
                                }
                                if (!empty($address['locality'])) {
                                    $address_parts[] = $address['locality'];
                                }
                                if (!empty($address['administrativeArea'])) {
                                    $address_parts[] = $address['administrativeArea'];
                                }
                                if (!empty($address['postalCode'])) {
                                    $address_parts[] = $address['postalCode'];
                                }
                                $full_address = implode(', ', $address_parts);
                            }

                            // Get features/activities
                            $features = get_the_terms(get_the_ID(), 'features');
                            $feature_names = array();
                            if ($features && !is_wp_error($features)) {
                                foreach ($features as $feature) {
                                    $feature_names[] = $feature->name;
                                }
                            }
                            ?>
                            <div class="petting-zoo-card">
                                <div class="card-image">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', array('alt' => get_the_title(), 'loading' => 'lazy')); ?>
                                        <?php else : ?>
                                            <img src="<?php echo wp_upload_dir()['baseurl']; ?>/2025/06/pettingzoophoto%20(1).webp" alt="<?php the_title(); ?>" loading="lazy">
                                        <?php endif; ?>
                                    </a>
                                    <?php if ($rating) : ?>
                                        <div class="rating-badge">
                                            <span class="rating-number"><?php echo number_format($rating, 1); ?></span>
                                            <span class="rating-star">★</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="card-content">
                                    <h3 class="zoo-name text-center">
                                        <?php the_title(); ?>
                                    </h3>

                                    <?php if ($full_address) : ?>
                                        <div class="zoo-location text-center">
                                            <span class="location-icon">📍</span>
                                            <?php echo esc_html($full_address); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($feature_names)) : ?>
                                        <div class="activities-section text-center">
                                            <div class="activity-tags">
                                                <?php
                                                $displayed_features = array_slice($feature_names, 0, 4);
                                                foreach ($displayed_features as $feature) : ?>
                                                    <span class="activity-tag"><?php echo esc_html($feature); ?></span>
                                                <?php endforeach; ?>
                                                <?php if (count($feature_names) > 4) : ?>
                                                    <span class="activity-tag more-tag">+<?php echo count($feature_names) - 4; ?> more</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="card-description text-center">
                                        <?php echo wp_trim_words(get_the_excerpt(), 10, '...'); ?>
                                    </div>

                                    <a href="<?php the_permalink(); ?>" class="view-details-btn">
                                        View Details →
                                    </a>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
            </section>

            <!-- Indoor Petting Zoos in Alabama -->
            <section class="state-indoor-section">
                <h2>Best Indoor Petting Zoos in Alabama</h2>
                <p>Enjoy year-round animal experiences at these indoor petting zoos across Alabama. Perfect for rainy days or extreme weather, these climate-controlled facilities offer comfortable environments for families to interact with animals regardless of outdoor conditions.</p>

                <div class="petting-zoo-cards-grid" id="state-indoor-zoos">
                    <?php
                    // Get indoor petting zoos
                    $indoor_args = array(
                        'post_type' => 'petting_zoo',
                        'posts_per_page' => 3,
                        'tax_query' => array(
                            'relation' => 'AND',
                            array(
                                'taxonomy' => 'location',
                                'field' => 'slug',
                                'terms' => 'alabama',
                            ),
                            array(
                                'taxonomy' => 'features',
                                'field' => 'slug',
                                'terms' => array('indoor', 'climate-controlled', 'indoor-activities'),
                                'operator' => 'IN'
                            )
                        )
                    );

                    $indoor_query = new WP_Query($indoor_args);

                    if (!$indoor_query->have_posts()) {
                        // Fallback: get any Alabama zoos
                        $indoor_args['tax_query'] = array(
                            array(
                                'taxonomy' => 'location',
                                'field' => 'slug',
                                'terms' => 'alabama',
                            )
                        );
                        $indoor_query = new WP_Query($indoor_args);
                    }

                    if ($indoor_query->have_posts()) :
                        $card_count = 0;
                        while ($indoor_query->have_posts() && $card_count < 3) : $indoor_query->the_post();
                            $card_count++;
                            // Get zoo data for consistent formatting
                            $zoo_data = get_post_meta(get_the_ID(), 'zoo_data', true);
                            $zoo_data = $zoo_data ? json_decode($zoo_data, true) : array();

                            $rating = isset($zoo_data['rating']) ? $zoo_data['rating'] : 4.5;
                            $address = isset($zoo_data['postalAddress']) ? $zoo_data['postalAddress'] : array();
                            $full_address = '';
                            if (!empty($address)) {
                                $address_parts = array();
                                if (!empty($address['addressLines'])) {
                                    $address_parts[] = $address['addressLines'][0];
                                }
                                if (!empty($address['locality'])) {
                                    $address_parts[] = $address['locality'];
                                }
                                if (!empty($address['administrativeArea'])) {
                                    $address_parts[] = $address['administrativeArea'];
                                }
                                if (!empty($address['postalCode'])) {
                                    $address_parts[] = $address['postalCode'];
                                }
                                $full_address = implode(', ', $address_parts);
                            }

                            // Get features/activities
                            $features = get_the_terms(get_the_ID(), 'features');
                            $feature_names = array();
                            if ($features && !is_wp_error($features)) {
                                foreach ($features as $feature) {
                                    $feature_names[] = $feature->name;
                                }
                            }
                            ?>
                            <div class="petting-zoo-card">
                                <div class="card-image">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', array('alt' => get_the_title(), 'loading' => 'lazy')); ?>
                                        <?php else : ?>
                                            <img src="<?php echo wp_upload_dir()['baseurl']; ?>/2025/06/pettingzoophoto%20(1).webp" alt="<?php the_title(); ?>" loading="lazy">
                                        <?php endif; ?>
                                    </a>
                                    <?php if ($rating) : ?>
                                        <div class="rating-badge">
                                            <span class="rating-number"><?php echo number_format($rating, 1); ?></span>
                                            <span class="rating-star">★</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="card-content">
                                    <h3 class="zoo-name text-center">
                                        <?php the_title(); ?>
                                    </h3>

                                    <?php if ($full_address) : ?>
                                        <div class="zoo-location text-center">
                                            <span class="location-icon">📍</span>
                                            <?php echo esc_html($full_address); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($feature_names)) : ?>
                                        <div class="activities-section text-center">
                                            <div class="activity-tags">
                                                <?php
                                                $displayed_features = array_slice($feature_names, 0, 4);
                                                foreach ($displayed_features as $feature) : ?>
                                                    <span class="activity-tag"><?php echo esc_html($feature); ?></span>
                                                <?php endforeach; ?>
                                                <?php if (count($feature_names) > 4) : ?>
                                                    <span class="activity-tag more-tag">+<?php echo count($feature_names) - 4; ?> more</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="card-description text-center">
                                        <?php echo wp_trim_words(get_the_excerpt(), 10, '...'); ?>
                                    </div>

                                    <a href="<?php the_permalink(); ?>" class="view-details-btn">
                                        View Details →
                                    </a>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
            </section>

            <!-- All Petting Zoos in Alabama -->
            <section class="all-zoos-section">
                <h2>All Petting Zoos in Alabama</h2>
                <p>Discover all the amazing petting zoos across Alabama. Use our interactive map and filters to find the perfect family destination near you.</p>

                <!-- Filter Buttons -->
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">All Zoos</button>
                    <button class="filter-btn" data-filter="family-friendly">Family Friendly</button>
                    <button class="filter-btn" data-filter="birthday-parties">Birthday Parties</button>
                    <button class="filter-btn" data-filter="educational">Educational</button>
                    <button class="filter-btn" data-filter="accessible">Accessible</button>
                </div>

                <!-- Google Maps -->
                <div class="map-container" style="margin: 30px 0;">
                    <div id="alabama-zoos-map" style="height: 400px; width: 100%; border-radius: 8px;"></div>
                </div>

                <!-- Petting Zoo Cards Grid -->
                <div class="petting-zoo-cards-grid" id="all-alabama-zoos">
                    <?php
                    // Get all Alabama petting zoos (max 9 for 3 rows)
                    $all_zoos_args = array(
                        'post_type' => 'petting_zoo',
                        'posts_per_page' => 9,
                        'tax_query' => array(
                            array(
                                'taxonomy' => 'location',
                                'field' => 'slug',
                                'terms' => 'alabama',
                            )
                        ),
                        'meta_key' => 'zoo_data',
                        'orderby' => 'meta_value',
                        'order' => 'DESC'
                    );

                    $all_zoos_query = new WP_Query($all_zoos_args);
                    $map_locations = array(); // For Google Maps

                    if ($all_zoos_query->have_posts()) :
                        while ($all_zoos_query->have_posts()) : $all_zoos_query->the_post();
                            // Get zoo data for consistent formatting
                            $zoo_data = get_post_meta(get_the_ID(), 'zoo_data', true);
                            $zoo_data = $zoo_data ? json_decode($zoo_data, true) : array();

                            $rating = isset($zoo_data['rating']) ? $zoo_data['rating'] : 4.5;
                            $address = isset($zoo_data['postalAddress']) ? $zoo_data['postalAddress'] : array();
                            $full_address = '';
                            if (!empty($address)) {
                                $address_parts = array();
                                if (!empty($address['addressLines'])) {
                                    $address_parts[] = $address['addressLines'][0];
                                }
                                if (!empty($address['locality'])) {
                                    $address_parts[] = $address['locality'];
                                }
                                if (!empty($address['administrativeArea'])) {
                                    $address_parts[] = $address['administrativeArea'];
                                }
                                if (!empty($address['postalCode'])) {
                                    $address_parts[] = $address['postalCode'];
                                }
                                $full_address = implode(', ', $address_parts);
                            }

                            // Get features/activities for filtering
                            $features = get_the_terms(get_the_ID(), 'features');
                            $feature_names = array();
                            $filter_classes = array();
                            if ($features && !is_wp_error($features)) {
                                foreach ($features as $feature) {
                                    $feature_names[] = $feature->name;
                                    $filter_classes[] = sanitize_title($feature->name);
                                }
                            }

                            // Add location data for map
                            if (isset($zoo_data['geo']) && isset($zoo_data['geo']['latitude']) && isset($zoo_data['geo']['longitude'])) {
                                $map_locations[] = array(
                                    'lat' => $zoo_data['geo']['latitude'],
                                    'lng' => $zoo_data['geo']['longitude'],
                                    'title' => get_the_title(),
                                    'address' => $full_address,
                                    'url' => get_permalink(),
                                    'rating' => $rating
                                );
                            }
                            ?>
                            <div class="petting-zoo-card" data-filters="<?php echo implode(' ', $filter_classes); ?>">
                                <div class="card-image">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', array('alt' => get_the_title(), 'loading' => 'lazy')); ?>
                                        <?php else : ?>
                                            <img src="<?php echo wp_upload_dir()['baseurl']; ?>/2025/06/pettingzoophoto%20(1).webp" alt="<?php the_title(); ?>" loading="lazy">
                                        <?php endif; ?>
                                    </a>
                                    <?php if ($rating) : ?>
                                        <div class="rating-badge">
                                            <span class="rating-number"><?php echo number_format($rating, 1); ?></span>
                                            <span class="rating-star">★</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="card-content">
                                    <h3 class="zoo-name text-center">
                                        <?php the_title(); ?>
                                    </h3>

                                    <?php if ($full_address) : ?>
                                        <div class="zoo-location text-center">
                                            <span class="location-icon">📍</span>
                                            <?php echo esc_html($full_address); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($feature_names)) : ?>
                                        <div class="activities-section text-center">
                                            <div class="activity-tags">
                                                <?php
                                                $displayed_features = array_slice($feature_names, 0, 4);
                                                foreach ($displayed_features as $feature) : ?>
                                                    <span class="activity-tag"><?php echo esc_html($feature); ?></span>
                                                <?php endforeach; ?>
                                                <?php if (count($feature_names) > 4) : ?>
                                                    <span class="activity-tag more-tag">+<?php echo count($feature_names) - 4; ?> more</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="card-description text-center">
                                        <?php echo wp_trim_words(get_the_excerpt(), 10, '...'); ?>
                                    </div>

                                    <a href="<?php the_permalink(); ?>" class="view-details-btn">
                                        View Details →
                                    </a>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>

                <!-- JavaScript for Map and Filtering -->
                <script>
                let alabamaZoosMap;
                let alabamaZoosMarkers = [];
                const alabamaMapLocations = <?php echo json_encode($map_locations); ?>;

                function initAlabamaZoosMap() {
                    const mapCenter = { lat: 32.806671, lng: -86.791130 }; // Alabama center

                    alabamaZoosMap = new google.maps.Map(document.getElementById('alabama-zoos-map'), {
                        zoom: 7,
                        center: mapCenter,
                        styles: [
                            {
                                featureType: 'poi',
                                elementType: 'labels',
                                stylers: [{ visibility: 'off' }]
                            }
                        ]
                    });

                    // Add markers for each zoo
                    alabamaMapLocations.forEach(location => {
                        const marker = new google.maps.Marker({
                            position: { lat: parseFloat(location.lat), lng: parseFloat(location.lng) },
                            map: alabamaZoosMap,
                            title: location.title,
                            icon: {
                                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                    <svg width="30" height="40" viewBox="0 0 30 40" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M15 0C6.716 0 0 6.716 0 15c0 8.284 15 25 15 25s15-16.716 15-25C30 6.716 23.284 0 15 0z" fill="#4CAF50"/>
                                        <circle cx="15" cy="15" r="8" fill="white"/>
                                        <text x="15" y="19" text-anchor="middle" font-family="Arial" font-size="10" fill="#4CAF50" font-weight="bold">${location.rating}</text>
                                    </svg>
                                `),
                                scaledSize: new google.maps.Size(30, 40),
                                anchor: new google.maps.Point(15, 40)
                            }
                        });

                        const infoWindow = new google.maps.InfoWindow({
                            content: `
                                <div style="padding: 10px; max-width: 250px;">
                                    <h4 style="margin: 0 0 5px 0; color: #2c5530;">${location.title}</h4>
                                    <p style="margin: 0 0 5px 0; font-size: 14px; color: #666;">${location.address}</p>
                                    <div style="margin: 5px 0;">
                                        <span style="color: #ffa500; font-weight: bold;">★ ${location.rating}</span>
                                    </div>
                                    <a href="${location.url}" style="color: #4CAF50; text-decoration: none; font-weight: bold;">View Details →</a>
                                </div>
                            `
                        });

                        marker.addListener('click', () => {
                            infoWindow.open(alabamaZoosMap, marker);
                        });

                        alabamaZoosMarkers.push(marker);
                    });
                }

                // Filter functionality
                document.addEventListener('DOMContentLoaded', function() {
                    const filterButtons = document.querySelectorAll('.filter-btn');
                    const zooCards = document.querySelectorAll('.petting-zoo-card[data-filters]');

                    filterButtons.forEach(button => {
                        button.addEventListener('click', function() {
                            // Update active button
                            filterButtons.forEach(btn => btn.classList.remove('active'));
                            this.classList.add('active');

                            const filter = this.getAttribute('data-filter');

                            // Show/hide cards based on filter
                            zooCards.forEach(card => {
                                if (filter === 'all') {
                                    card.style.display = 'block';
                                } else {
                                    const cardFilters = card.getAttribute('data-filters');
                                    if (cardFilters && cardFilters.includes(filter)) {
                                        card.style.display = 'block';
                                    } else {
                                        card.style.display = 'none';
                                    }
                                }
                            });
                        });
                    });

                    // Initialize map when Google Maps API is loaded
                    if (typeof google !== 'undefined' && google.maps) {
                        initAlabamaZoosMap();
                    } else {
                        // Wait for Google Maps API to load
                        window.initAlabamaZoosMap = initAlabamaZoosMap;
                    }
                });

                // FAQ functionality
                const faqQuestions = document.querySelectorAll('.faq-question');
                faqQuestions.forEach(question => {
                    question.addEventListener('click', function() {
                        const faqItem = this.parentElement;
                        const isActive = faqItem.classList.contains('active');

                        // Close all FAQ items
                        document.querySelectorAll('.faq-item').forEach(item => {
                            item.classList.remove('active');
                        });

                        // Open clicked item if it wasn't active
                        if (!isActive) {
                            faqItem.classList.add('active');
                        }
                    });
                });
                </script>
            </section>

            <!-- Frequently Asked Questions -->
            <section class="faq-section">
                <h2>Frequently Asked Questions</h2>

                <div class="faq-item">
                    <h3 class="faq-question">What are the best petting zoos in Alabama?</h3>
                    <div class="faq-answer">
                        <p>Alabama offers several excellent petting zoos including the Montgomery Zoo, Birmingham Zoo's children's area, and various farm-based petting zoos throughout the state. Each offers unique animal experiences and educational programs for families.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <h3 class="faq-question">Are Alabama petting zoos suitable for toddlers?</h3>
                    <div class="faq-answer">
                        <p>Yes, most Alabama petting zoos are designed with young children in mind. They feature gentle animals, safe enclosures, and supervised interactions. Many also offer stroller-friendly paths and changing facilities.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <h3 class="faq-question">What animals can children interact with at Alabama petting zoos?</h3>
                    <div class="faq-answer">
                        <p>Common animals include goats, sheep, rabbits, chickens, miniature horses, and sometimes llamas or alpacas. Some locations also feature educational programs with reptiles, birds of prey, or farm animals.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <h3 class="faq-question">Do Alabama petting zoos offer birthday party packages?</h3>
                    <div class="faq-answer">
                        <p>Many Alabama petting zoos offer birthday party packages that include animal interactions, educational presentations, and sometimes party areas. Contact individual locations for specific packages and pricing.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <h3 class="faq-question">What should we bring to a petting zoo in Alabama?</h3>
                    <div class="faq-answer">
                        <p>Bring hand sanitizer, comfortable walking shoes, sun protection, and a camera. Some locations provide animal feed, while others allow you to bring approved treats. Check with the specific zoo for their policies.</p>
                    </div>
                </div>
            </section>

            <!-- Cities in Alabama -->
            <?php
            // Get child locations (cities) in Alabama
            $child_locations = get_terms(array(
                'taxonomy' => 'location',
                'parent' => $alabama_term ? $alabama_term->term_id : 0,
                'hide_empty' => false,
                'orderby' => 'name',
                'order' => 'ASC'
            ));

            if (!empty($child_locations) && !is_wp_error($child_locations)) : ?>
                <section class="child-locations section">
                    <h2>Cities in Alabama</h2>
                    <div class="cities-grid">
                        <?php foreach ($child_locations as $city) : ?>
                            <a href="<?php echo get_term_link($city); ?>" class="city-card">
                                <h4><?php echo esc_html($city->name); ?></h4>
                                <div class="zoo-count"><?php echo $city->count; ?> Petting Zoo<?php echo $city->count !== 1 ? 's' : ''; ?></div>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </section>
            <?php endif; ?>

        </div>
    </main>
</div>

<?php get_footer(); ?>
